import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import { RefreshCw, Filter, Search, ExternalLink } from 'lucide-react';
import CreateServiceButton from '../components/common/CreateServiceButton';
import { useServices } from '../hooks/useServices';
import { usePageTitle } from '../hooks/usePageTitle';



const Services = () => {
  usePageTitle('Services');

  const {
    services,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    totalServices,
    activeServices,
    loading,
    refreshServices
  } = useServices();

  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Services</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your managed services
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="secondary"
            size="sm"
            onClick={refreshServices}
            disabled={loading}
          >
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <CreateServiceButton />
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Search services..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center">
            <div className="relative inline-block text-left">
              <div>
                <button
                  type="button"
                  className="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500"
                  onClick={() => setIsFilterOpen(!isFilterOpen)}
                >
                  <Filter size={16} className="mr-2" />
                  Filter
                </button>
              </div>
              {isFilterOpen && (
                <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                  <div className="py-1" role="menu" aria-orientation="vertical">
                    <button
                      className={`block px-4 py-2 text-sm w-full text-left ${
                        statusFilter === 'all' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                      onClick={() => {
                        setStatusFilter('all');
                        setIsFilterOpen(false);
                      }}
                    >
                      All Services
                    </button>
                    <button
                      className={`block px-4 py-2 text-sm w-full text-left ${
                        statusFilter === 'active' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                      onClick={() => {
                        setStatusFilter('active');
                        setIsFilterOpen(false);
                      }}
                    >
                      Active Services
                    </button>
                    <button
                      className={`block px-4 py-2 text-sm w-full text-left ${
                        statusFilter === 'suspended' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                      onClick={() => {
                        setStatusFilter('suspended');
                        setIsFilterOpen(false);
                      }}
                    >
                      Suspended Services
                    </button>
                    <button
                      className={`block px-4 py-2 text-sm w-full text-left ${
                        statusFilter === 'stopped' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                      onClick={() => {
                        setStatusFilter('stopped');
                        setIsFilterOpen(false);
                      }}
                    >
                      Stopped Services
                    </button>
                    <button
                      className={`block px-4 py-2 text-sm w-full text-left ${
                        statusFilter === 'provisioning' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                      onClick={() => {
                        setStatusFilter('provisioning');
                        setIsFilterOpen(false);
                      }}
                    >
                      Provisioning Services
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>



      {/* Services Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                // Loading skeleton UI
                Array.from({ length: 5 }).map((_, index) => (
                  <tr key={`skeleton-${index}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="h-8 bg-gray-200 rounded animate-pulse w-16 ml-auto"></div>
                    </td>
                  </tr>
                ))
              ) : services.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-gray-500">
                    No services found matching your criteria
                  </td>
                </tr>
              ) : (
                services.map((service) => (
                  <tr key={service.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link
                        to={`/dashboard/services/${service.id}`}
                        className="group flex items-center text-sm font-medium text-gray-900 hover:text-blue-600"
                      >
                        <div>
                          <div className="flex items-center">
                            {service.name}
                            <ExternalLink size={14} className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{service.service_templates?.name || 'Unknown'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        service.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : service.status === 'suspended'
                          ? 'bg-yellow-100 text-yellow-800'
                          : service.status === 'stopped'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {service.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {service.plan}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatPrice(service.price)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="whitespace-pre-line">
                        {service.expiryFormatted}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        as={Link}
                        to={`/dashboard/services/${service.id}`}
                        variant="secondary"
                        size="sm"
                      >
                        Manage
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Services;
