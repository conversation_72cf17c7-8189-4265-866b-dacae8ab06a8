import React, { useState, useMemo, useRef, useEffect } from 'react';
import { ArrowLeft, Cloud, Cpu, MemoryStick, DollarSign, Zap } from 'lucide-react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import Button from '../components/common/Button';
import { useServiceTemplatesByType } from '../hooks/useServiceTemplates';
import { useCreateService } from '../hooks/useCreateService';
import { usePageTitle } from '../hooks/usePageTitle';

// Add CSS for description container with 2-line limit and ellipsis
const descriptionStyles = `
  .description-container {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
  }
`;

const CreateServiceByType = () => {
  const navigate = useNavigate();
  const { type } = useParams<{ type: string }>();
  const { templates, loading: templatesLoading, error: templatesError } = useServiceTemplatesByType(type || null);
  const { createService } = useCreateService();

  // Set dynamic page title based on service type
  usePageTitle(type ? `Deploy ${type.charAt(0).toUpperCase() + type.slice(1)}` : 'Deploy Service');

  // Form state
  const [serviceName, setServiceName] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [isDeploying, setIsDeploying] = useState(false);
  const [deploymentError, setDeploymentError] = useState<string | null>(null);
  const [serviceNameTouched, setServiceNameTouched] = useState(false);

  // Ref for auto-focus
  const serviceNameInputRef = useRef<HTMLInputElement>(null);

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Auto-focus on service name input when component mounts
  useEffect(() => {
    if (serviceNameInputRef.current) {
      serviceNameInputRef.current.focus();
    }
  }, []);

  // Auto-select first template when templates load
  React.useEffect(() => {
    if (templates.length > 0 && !selectedTemplate) {
      const sortedTemplates = [...templates].sort((a, b) => a.base_price - b.base_price);
      setSelectedTemplate(sortedTemplates[0]);
      if (sortedTemplates[0].available_plans?.plans?.length > 0) {
        setSelectedPlan(sortedTemplates[0].available_plans.plans[0]);
      }
    }
  }, [templates, selectedTemplate]);

  // Format RAM in MB or GB
  const formatRAM = (ramMB: number | null) => {
    if (!ramMB) return 'N/A';
    if (ramMB >= 1024) {
      return `${(ramMB / 1024).toFixed(1)} GB`;
    }
    return `${ramMB} MB`;
  };

  // Validation logic
  const isServiceNameValid = serviceName.trim().length >= 3;
  const showServiceNameError = serviceNameTouched && !isServiceNameValid;

  const handleDeploy = async () => {
    // Mark service name as touched to show validation errors
    setServiceNameTouched(true);

    if (!isServiceNameValid) {
      setDeploymentError('Service name must be at least 3 characters long');
      return;
    }

    if (!selectedTemplate) {
      setDeploymentError('Please select a template');
      return;
    }

    if (!selectedPlan) {
      setDeploymentError('Please select a plan');
      return;
    }

    setIsDeploying(true);
    setDeploymentError(null);

    try {
      const result = await createService({
        templateId: selectedTemplate.id,
        name: serviceName.trim(),
        plan: selectedPlan
      });

      if (result.error) {
        setDeploymentError(result.error);
      } else {
        // On success, redirect to service detail page
        navigate(`/dashboard/services/${result.serviceId}`);
      }
    } catch (err: any) {
      setDeploymentError(err.message || 'An unexpected error occurred');
    } finally {
      setIsDeploying(false);
    }
  };

  // Get service type name for display
  const getServiceTypeName = (type: string) => {
    switch (type) {
      case 'n8n':
        return 'n8n';
      case 'waha':
        return 'WAHA Cloud';
      default:
        return type;
    }
  };

  // Get billing plan information (no discounts)
  const getBillingPlanInfo = (planType: string, basePrice: number) => {
    switch (planType) {
      case 'monthly':
        return {
          name: 'Monthly',
          description: 'Billed monthly',
          price: basePrice,
          period: 'month',
          discount: null
        };
      case 'quarterly':
        return {
          name: 'Quarterly',
          description: 'Billed every 3 months',
          price: basePrice * 3,
          period: '3 months',
          discount: null
        };
      case 'biannual':
        return {
          name: 'Biannual',
          description: 'Billed every 6 months',
          price: basePrice * 6,
          period: '6 months',
          discount: null
        };
      case 'yearly':
        return {
          name: 'Yearly',
          description: 'Billed annually',
          price: basePrice * 12,
          period: 'year',
          discount: null
        };
      default:
        return {
          name: planType,
          description: 'Custom billing cycle',
          price: basePrice,
          period: 'month',
          discount: null
        };
    }
  };

  // Helper function to check if template is available (has quota)
  const isTemplateAvailable = (template: any) => {
    // Check available_quota field (not quota)
    // null means unlimited quota (available)
    // 0 or negative means no quota (unavailable)
    return template.available_quota === null || template.available_quota > 0;
  };

  // Helper function to get quota status display
  const getQuotaStatus = (template: any) => {
    if (template.available_quota === null) {
      // Unlimited quota - don't show anything
      return null;
    } else if (template.available_quota <= 0) {
      // Out of quota
      return {
        text: 'Out of Quota',
        className: 'text-red-500 bg-red-50 px-2 py-1 rounded-full text-xs font-medium'
      };
    } else {
      // Has remaining quota
      return {
        text: `${template.available_quota} remaining`,
        className: 'text-green-600 bg-green-50 px-2 py-1 rounded-full text-xs font-medium'
      };
    }
  };

  // Sort templates by price (cheapest first) and separate available/unavailable
  const { availableTemplates, unavailableTemplates } = useMemo(() => {
    const available = templates.filter(isTemplateAvailable);
    const unavailable = templates.filter(template => !isTemplateAvailable(template));

    return {
      availableTemplates: [...available].sort((a, b) => a.base_price - b.base_price),
      unavailableTemplates: [...unavailable].sort((a, b) => a.base_price - b.base_price)
    };
  }, [templates]);

  // All templates sorted (available first, then unavailable)
  const sortedTemplates = useMemo(() => {
    return [...availableTemplates, ...unavailableTemplates];
  }, [availableTemplates, unavailableTemplates]);

  // Auto-select first available template when templates load
  React.useEffect(() => {
    if (availableTemplates.length > 0 && !selectedTemplate) {
      setSelectedTemplate(availableTemplates[0]);

      // Auto-select first plan if available
      if (availableTemplates[0].available_plans?.plans?.length > 0) {
        setSelectedPlan(availableTemplates[0].available_plans.plans[0]);
      }
    }
  }, [availableTemplates, selectedTemplate]);

  // Calculate estimated cost
  const estimatedCost = useMemo(() => {
    if (!selectedTemplate || !selectedPlan) return 0;
    const planInfo = getBillingPlanInfo(selectedPlan, selectedTemplate.base_price);
    return planInfo.price;
  }, [selectedTemplate, selectedPlan]);

  // Get selected plan display info
  const selectedPlanInfo = useMemo(() => {
    if (!selectedTemplate || !selectedPlan) return null;
    return getBillingPlanInfo(selectedPlan, selectedTemplate.base_price);
  }, [selectedTemplate, selectedPlan]);

  return (
    <>
      <div className="space-y-6 pb-20 md:pb-6">
        {/* Header */}
        <div className="flex items-center">
          <Link to="/dashboard/services/create" className="mr-4">
            <Button variant="secondary" size="sm">
              <ArrowLeft size={16} className="mr-2" />
              Back to Categories
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Deploy {getServiceTypeName(type || '')}</h1>
            <p className="mt-1 text-sm text-gray-500">
              Configure and deploy your service in seconds
            </p>
          </div>
        </div>

      {templatesLoading ? (
        <div className="py-10 text-center text-gray-500">
          <Cloud className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          Loading templates...
        </div>
      ) : templatesError ? (
        <div className="py-10 text-center text-red-500">
          Error loading templates: {templatesError}
        </div>
      ) : sortedTemplates.length === 0 ? (
        <div className="py-10 text-center text-gray-500">
          No templates found for this category
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Configuration Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Service Name */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Configuration</h3>
              <div>
                <label htmlFor="serviceName" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Name
                </label>
                <input
                  ref={serviceNameInputRef}
                  type="text"
                  id="serviceName"
                  value={serviceName}
                  onChange={(e) => setServiceName(e.target.value)}
                  onBlur={() => setServiceNameTouched(true)}
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 ${
                    showServiceNameError
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Enter your service name"
                />
                {showServiceNameError && (
                  <p className="mt-2 text-sm text-red-600">
                    Service name must be at least 3 characters long
                  </p>
                )}
              </div>
            </div>

            {/* Template Selection */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Template</h3>
              <div className="grid grid-cols-1 gap-4">
                {sortedTemplates.map((template) => {
                  const isAvailable = isTemplateAvailable(template);
                  return (
                    <div
                      key={template.id}
                      className={`relative border rounded-lg p-4 transition-all ${
                        !isAvailable
                          ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
                          : selectedTemplate?.id === template.id
                          ? 'border-blue-500 bg-blue-50 cursor-pointer'
                          : 'border-gray-200 hover:border-gray-300 cursor-pointer'
                      }`}
                      onClick={() => {
                        if (isAvailable) {
                          setSelectedTemplate(template);
                          if (template.available_plans?.plans?.length > 0) {
                            setSelectedPlan(template.available_plans.plans[0]);
                          }
                        }
                      }}
                    >
                    {selectedTemplate?.id === template.id && (
                      <div className="absolute top-2 right-2">
                        <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Cloud className="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900">{template.name}</h4>
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">{template.description}</p>

                        {/* CPU and RAM */}
                        <div className="flex items-center space-x-3 mt-2 text-xs text-gray-600">
                          {template.cpu_limit && (
                            <div className="flex items-center">
                              <Cpu size={12} className="mr-1" />
                              <span>{template.cpu_limit} CPU</span>
                            </div>
                          )}
                          {template.ram_limit && (
                            <div className="flex items-center">
                              <MemoryStick size={12} className="mr-1" />
                              <span>{formatRAM(template.ram_limit)} RAM</span>
                            </div>
                          )}
                        </div>

                        <div className="mt-2 flex items-center justify-between">
                          <span className={`text-sm font-medium ${!isAvailable ? 'text-gray-400' : 'text-gray-900'}`}>
                            {template.base_price === 0
                              ? 'FREE'
                              : `${formatPrice(template.base_price)}/month`
                            }
                          </span>
                          {(() => {
                            const quotaStatus = getQuotaStatus(template);
                            return quotaStatus ? (
                              <span className={quotaStatus.className}>
                                {quotaStatus.text}
                              </span>
                            ) : null;
                          })()}
                        </div>
                      </div>
                    </div>
                  </div>
                  );
                })}
              </div>
            </div>

            {/* Billing Cycle Selection */}
            {selectedTemplate && selectedTemplate.available_plans?.plans?.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Billing Cycle</h3>
                <div className="grid grid-cols-1 gap-3">
                  {selectedTemplate.available_plans.plans.map((planType: string, index: number) => {
                    const planInfo = getBillingPlanInfo(planType, selectedTemplate.base_price);
                    return (
                      <div
                        key={index}
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          selectedPlan === planType
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedPlan(planType)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">{planInfo.name}</h4>
                            <p className="text-xs text-gray-500 mt-1">{planInfo.description}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-900">
                              {formatPrice(planInfo.price)}/{planInfo.period}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

          </div>

          {/* Right Column - Desktop Deployment Summary (Hidden on Mobile) */}
          <div className="hidden lg:block lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Deployment Summary</h3>

              {selectedTemplate && (
                <div className="space-y-4">
                  <div>
                    <div className="text-sm text-gray-500">Service Type</div>
                    <div className="font-medium">{getServiceTypeName(type || '')}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-500">Template</div>
                    <div className="font-medium">{selectedTemplate.name}</div>
                  </div>

                  {selectedPlanInfo && (
                    <div>
                      <div className="text-sm text-gray-500">Billing Cycle</div>
                      <div className="font-medium">{selectedPlanInfo.name}</div>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between text-lg font-semibold">
                      <span>Cost</span>
                      <span className="text-blue-600">
                        {estimatedCost === 0 ? 'FREE' : `${formatPrice(estimatedCost)}${selectedPlanInfo ? `/${selectedPlanInfo.period}` : '/month'}`}
                      </span>
                    </div>
                  </div>

                  {deploymentError && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-3">
                      <div className="text-sm text-red-600">{deploymentError}</div>
                    </div>
                  )}

                  <Button
                    variant="primary"
                    className="w-full"
                    onClick={handleDeploy}
                    disabled={isDeploying || !isServiceNameValid || !selectedTemplate || !selectedPlan}
                  >
                    {isDeploying ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Deploying...
                      </>
                    ) : (
                      <>
                        <Zap size={18} className="mr-2" />
                        Deploy Service
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          </div>


        </div>
      )}
      </div>

      {/* Mobile Sticky Bottom Bar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="text-xs text-gray-500">Estimated Cost</div>
            <div className="text-lg font-semibold text-blue-600">
              {estimatedCost === 0 ? 'FREE' : `${formatPrice(estimatedCost)}${selectedPlanInfo ? `/${selectedPlanInfo.period}` : '/month'}`}
            </div>
          </div>
          <Button
            variant="primary"
            className="ml-4 px-6"
            onClick={handleDeploy}
            disabled={isDeploying || !isServiceNameValid || !selectedTemplate || !selectedPlan}
          >
            {isDeploying ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Deploying...
              </>
            ) : (
              <>
                <Zap size={18} className="mr-2" />
                Deploy
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
};

export default CreateServiceByType;
