import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { usePods } from '../hooks/usePods';
import PodHeader from '../components/pods/PodHeader';
import PodStatus from '../components/pods/PodStatus';
import PodMetrics from '../components/pods/PodMetrics';
import PodLogs from '../components/pods/PodLogs';
import Button from '../components/common/Button';
import { usePageTitle } from '../hooks/usePageTitle';

const PodDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { pod } = usePods(id);

  // Set dynamic page title based on pod name
  usePageTitle(pod ? `${pod.name} - Pod Details` : 'Pod Details');

  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isRestarting, setIsRestarting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  if (!pod) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Pod not found</p>
        <Button as={Link} to="/dashboard" variant="primary" className="mt-4">
          Back to Dashboard
        </Button>
      </div>
    );
  }

  const handleStart = async () => {
    setIsStarting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsStarting(false);
  };

  const handleStop = async () => {
    setIsStopping(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsStopping(false);
  };

  const handleRestart = async () => {
    setIsRestarting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRestarting(false);
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this pod?')) {
      setIsDeleting(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsDeleting(false);
      navigate('/dashboard');
    }
  };

  const handleRefreshLogs = async () => {
    // Implement log refresh logic here
    console.log('Refreshing logs...');
  };

  return (
    <div>
      {/* Section 1: Header with Pod Name and Image */}
      <PodHeader pod={pod} />

      {/* Section 2: Status and Actions */}
      <PodStatus
        pod={pod}
        onStart={handleStart}
        onStop={handleStop}
        onRestart={handleRestart}
        onDelete={handleDelete}
        isStarting={isStarting}
        isStopping={isStopping}
        isRestarting={isRestarting}
        isDeleting={isDeleting}
      />

      {/* Section 3: Resource Usage */}
      <PodMetrics pod={pod} />

      {/* Section 4: Logs */}
      {pod.logs && (
        <PodLogs
          logs={pod.logs}
          onRefresh={handleRefreshLogs}
        />
      )}
    </div>
  );
};

export default PodDetails;