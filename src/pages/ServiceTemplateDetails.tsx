import { useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useServiceTemplateBySlug } from '../hooks/useServiceTemplateBySlug';
import Button from '../components/common/Button';
import { usePageTitle } from '../hooks/usePageTitle';
import {
  ArrowLeft,
  Loader,
  Server,
  Cloud,
  Check,
  Calendar,
  DollarSign
} from 'lucide-react';

const ServiceTemplateDetails = () => {
  const { slug } = useParams<{ slug: string }>();
  const { template, loading, error } = useServiceTemplateBySlug(slug);
  const navigate = useNavigate();

  // Set dynamic page title based on template name
  usePageTitle(template ? `${template.name} - Service Template` : 'Service Template');

  // Format price to IDR
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <div className="text-gray-500">Loading service details...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-4">Error loading service details</div>
          <Button variant="secondary" onClick={() => navigate('/dashboard/services')}>
            Back to Services
          </Button>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-500 mb-4">Service not found</div>
          <Button variant="secondary" onClick={() => navigate('/dashboard/services')}>
            Back to Services
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header with back button and service name */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link to="/dashboard/services" className="mr-4">
            <Button variant="secondary" size="sm">
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Button>
          </Link>
        </div>
        <h1 className="text-3xl font-bold text-gray-900">{template.name}</h1>
        <p className="mt-2 text-lg text-gray-600">{template.description}</p>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left column - Service details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Service Overview Card */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Service Overview</h2>
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                <Server className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div className="text-sm text-gray-500">Type</div>
                <div className="font-medium">{template.type}</div>
              </div>
            </div>
            <p className="text-gray-700 mb-4">{template.description}</p>

            {/* Features list - This would be dynamic in a real implementation */}
            <h3 className="text-lg font-medium text-gray-900 mb-3">Features</h3>
            <ul className="space-y-2">
              {template.slug_id === 'waha' && (
                <>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>WhatsApp API Gateway</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>Fully managed hosting</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>24/7 uptime monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>REST API access</span>
                  </li>
                </>
              )}
              {template.slug_id === 'warung' && (
                <>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>Headless eCommerce platform</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>Product management</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>Order processing</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>Customer management</span>
                  </li>
                  <li className="flex items-start">
                    <Check size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span>Payment gateway integration</span>
                  </li>
                </>
              )}
            </ul>
          </div>
        </div>

        {/* Right column - Pricing and action */}
        <div className="space-y-6">
          {/* Pricing Card */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Pricing</h2>
            <div className="mb-4">
              <div className="text-3xl font-bold text-gray-900">{formatPrice(template.base_price)}</div>
              <div className="text-sm text-gray-500">Base price</div>
            </div>

            {/* Available plans */}
            <h3 className="text-lg font-medium text-gray-900 mb-3">Available Plans</h3>
            <div className="space-y-2">
              {template.available_plans.plans.map((plan) => (
                <div key={plan} className="flex items-center">
                  <Calendar size={18} className="text-blue-500 mr-2" />
                  <span className="capitalize">{plan}</span>
                </div>
              ))}
            </div>

            <div className="mt-6">
              <Button
                variant="primary"
                className="w-full"
                onClick={() => navigate('/dashboard/services/create')}
              >
                <Cloud size={18} className="mr-2" />
                Deploy this service
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceTemplateDetails;
