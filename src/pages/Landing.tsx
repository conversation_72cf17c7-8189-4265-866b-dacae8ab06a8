import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Check, Box, Zap, Server } from 'lucide-react';
import Button from '../components/common/Button';
import { usePageTitle } from '../hooks/usePageTitle';

const Landing = () => {
  usePageTitle('Deploy Applications in 15 Seconds!');

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50 to-white -z-10"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-blue-600/5 rounded-full blur-3xl -z-10"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl lg:text-8xl font-bold text-gray-900 tracking-tight mb-12">
            <span className="text-blue-600">Deploy Applications</span><br className="hidden sm:inline" />
            in 15 Seconds!
          </h1>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button as={Link} to="/register" variant="primary" size="lg">
              Get Started
            </Button>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 md:py-28 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Simple, Transparent Pricing
            </h2>
            <p className="max-w-2xl mx-auto text-xl text-gray-600">
              Get started with SumoPod today and experience the power of container management
            </p>
          </div>

          <div className="max-w-lg mx-auto">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border-2 border-blue-500">
              <div className="p-10 text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Start Today</h3>
                <div className="flex items-baseline justify-center mb-6">
                  <span className="text-5xl font-extrabold text-blue-600">FREE</span>
                </div>
                <p className="text-gray-600 mb-8 leading-relaxed">
                  All the features you need to manage containers and applications effectively
                </p>
                <div className="space-y-4">
                  <Button
                    as={Link}
                    to="/register"
                    variant="primary"
                    size="lg"
                    className="w-full"
                  >
                    Get Started
                  </Button>
                  <Button
                    as={Link}
                    to="/#features"
                    variant="outline"
                    size="lg"
                    className="w-full"
                  >
                    Learn More
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 md:py-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Everything you need in one platform
            </h2>
            <p className="max-w-2xl mx-auto text-xl text-gray-600 mb-8">
              SumoPod offers comprehensive solutions for container and application management
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {/* Feature 1 */}
            <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <Box className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Container Marketplace</h3>
              <p className="text-gray-600 leading-relaxed">
                Explore and purchase from our extensive container library, all verified and ready for instant deployment.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <Server className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">One-Click Deployment</h3>
              <p className="text-gray-600 leading-relaxed">
                Deploy containers to your infrastructure with one click, eliminating complex configuration processes.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <Zap className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Automatic Updates</h3>
              <p className="text-gray-600 leading-relaxed">
                Keep your containers and applications up to date with automatic version updates and security patches.
              </p>
            </div>

          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 md:py-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-blue-600 rounded-2xl overflow-hidden shadow-xl">
            <div className="px-8 py-16 md:px-16 md:py-20 text-center md:text-left md:flex md:items-center md:justify-between">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">
                  Ready to transform your container management?
                </h2>
                <p className="text-lg text-blue-100 max-w-2xl leading-relaxed">
                  Join thousands of businesses using SumoPod to simplify their container and application infrastructure.
                </p>
              </div>
              <div className="mt-8 md:mt-0 flex flex-col md:flex-row gap-4 md:ml-8">
                <Button as={Link} to="/register" variant="secondary" size="lg" className="md:w-auto font-semibold">
                  Get Started
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Landing;