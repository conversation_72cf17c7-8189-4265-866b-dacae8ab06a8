import { useParams, Navigate } from 'react-router-dom';
import { useServiceTemplateBySlug } from '../hooks/useServiceTemplateBySlug';
import { Loader } from 'lucide-react';
import { usePageTitle } from '../hooks/usePageTitle';

// Import service-specific components
import WahaServiceDetails from '../components/services/WahaServiceDetails';
import WarungServiceDetails from '../components/services/WarungServiceDetails';
import PesanServiceDetails from '../components/services/PesanServiceDetails';
import BeetleHRServiceDetails from '../components/services/BeetleHRServiceDetails';
import DefaultServiceDetails from '../components/services/DefaultServiceDetails';

const ServiceTemplate = () => {
  const { slug } = useParams<{ slug: string }>();
  const { template, loading, error } = useServiceTemplateBySlug(slug);

  usePageTitle(template ? `${template.name} - Service Template` : 'Service Template');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <div className="text-gray-500">Loading service details...</div>
        </div>
      </div>
    );
  }

  if (error || !template) {
    return <Navigate to="/dashboard/services" replace />;
  }

  // Render the appropriate service detail page based on the slug_id
  switch (template.slug_id) {
    case 'waha':
      return <WahaServiceDetails service={template} />;
    case 'warung':
      return <WarungServiceDetails service={template} />;
    case 'pesan':
      return <PesanServiceDetails service={template} />;
    case 'beetlehr':
      return <BeetleHRServiceDetails service={template} />;
    default:
      // Use the default service details component when no specific component is found
      return <DefaultServiceDetails service={template} />;
  }
};

export default ServiceTemplate;
