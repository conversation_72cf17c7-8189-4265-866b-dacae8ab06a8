import React, { useState } from 'react';
import { ArrowLeft, Server } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import Button from '../components/common/Button';
import VMConfigModal, { VMConfig } from '../components/modals/VMConfigModal';
import { useVMTemplates } from '../hooks/useVMTemplates';
import { useCreateVM } from '../hooks/useCreateVM';
import { usePageTitle } from '../hooks/usePageTitle';

const CreateVM = () => {
  usePageTitle('Create VM');

  const navigate = useNavigate();
  const { templates, loading, error, searchQuery, setSearchQuery } = useVMTemplates();
  const { createVM, loading: creatingVM, error: createError } = useCreateVM();
  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [vmCreationStatus, setVMCreationStatus] = useState<{
    success: boolean;
    message: string;
    vmId: string | null;
  } | null>(null);

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template);
    setConfigModalOpen(true);
  };

  const handleConfigSubmit = async (config: VMConfig) => {
    try {
      // Call the createVM function
      const result = await createVM({
        templateId: selectedTemplate.id,
        name: config.name,
        plan: config.plan,
        resources: config.resources
      });

      if (result.error) {
        setVMCreationStatus({
          success: false,
          message: result.error,
          vmId: null
        });
      } else {
        setVMCreationStatus({
          success: true,
          message: 'VM created successfully! It is now being provisioned.',
          vmId: result.vmId
        });

        // Close the modal and navigate back to VMs list after a delay
        setTimeout(() => {
          setConfigModalOpen(false);
          navigate('/dashboard/vms');
        }, 2000);
      }
    } catch (err: any) {
      setVMCreationStatus({
        success: false,
        message: err.message || 'An unexpected error occurred',
        vmId: null
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Link to="/dashboard/vms" className="mr-4">
          <Button variant="secondary" size="sm">
            <ArrowLeft size={16} className="mr-2" />
            Back
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create VM</h1>
          <p className="mt-1 text-sm text-gray-500">
            Choose a VM template to create a new virtual machine
          </p>
        </div>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <input
          type="text"
          className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Search templates..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
          </svg>
        </div>
      </div>

      {/* Templates grid */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Loading templates...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      ) : templates.length === 0 ? (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative">
          <strong className="font-bold">No templates found: </strong>
          <span className="block sm:inline">Try a different search term or check back later.</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {templates.map((template) => (
            <div
              key={template.id}
              className="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleTemplateSelect(template)}
            >
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                    <Server className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-500">{template.type}</p>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-sm text-gray-500">{template.description}</p>
                </div>
                <div className="mt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-500">From</span>
                      <span className="ml-1 text-lg font-semibold text-gray-900">Rp {formatPrice(template.base_price)}</span>
                      <span className="ml-1 text-sm text-gray-500">/month</span>
                    </div>
                    <Button variant="primary" size="sm">
                      Select
                    </Button>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-4 sm:px-6">
                <div className="text-sm">
                  <div className="font-medium text-gray-500">Specifications:</div>
                  <div className="mt-1 text-gray-900">
                    {template.specs.cpu} vCPU • {template.specs.memory} GB RAM • {template.specs.storage} GB SSD
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* VM Configuration Modal */}
      {selectedTemplate && (
        <VMConfigModal
          isOpen={configModalOpen}
          onClose={() => setConfigModalOpen(false)}
          onSubmit={handleConfigSubmit}
          templateName={selectedTemplate.name}
          templateType={selectedTemplate.type}
          basePrice={selectedTemplate.base_price}
          availablePlans={selectedTemplate.available_plans?.plans || ['monthly', 'quarterly', 'yearly']}
          defaultResources={selectedTemplate.specs}
          status={vmCreationStatus}
        />
      )}
    </div>
  );
};

export default CreateVM;
