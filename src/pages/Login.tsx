import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Mail, KeyRound, AlertCircle, CheckCircle } from 'lucide-react';
import Button from '../components/common/Button';
import { useAuth } from '../context/AuthContext';
import GoogleIcon from '../components/icons/GoogleIcon';
import { usePageTitle } from '../hooks/usePageTitle';

const Login = () => {
  usePageTitle('Login');

  const [email, setEmail] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);

  const { requestOtp, verifyOtp, signInWithGoogle } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();


  // Get the redirect path from location state, or default to /dashboard
  const from = location.state?.from?.pathname || '/dashboard';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otpSent) {
      // Step 1: Request OTP
      if (!email) {
        setError('Please enter your email address');
        return;
      }

      setIsLoading(true);
      setError('');
      setSuccessMessage('');

      try {
        const { success, error } = await requestOtp(email);

        if (success) {
          setOtpSent(true);
          setSuccessMessage('A verification code has been sent to your email');
        } else {
          setError(error?.message || 'Failed to send verification code');
        }
      } catch (err) {
        setError('An error occurred. Please try again.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    } else {
      // Step 2: Verify OTP
      if (!otpCode) {
        setError('Please enter the verification code');
        return;
      }

      setIsLoading(true);
      setError('');

      try {
        const { success, error } = await verifyOtp(email, otpCode);

        if (success) {
          navigate(from, { replace: true });
        } else {
          setError(error?.message || 'Invalid verification code');
        }
      } catch (err) {
        setError('An error occurred during verification. Please try again.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const resetForm = () => {
    setOtpSent(false);
    setOtpCode('');
    setError('');
    setSuccessMessage('');
  };

  const handleGoogleSignIn = async () => {
    setGoogleLoading(true);
    setError('');

    try {
      const { success, error } = await signInWithGoogle();

      if (!success && error) {
        setError(error.message || 'Failed to sign in with Google');
      }
      // No need to navigate - the OAuth flow will redirect automatically
    } catch (err) {
      setError('An error occurred during Google sign-in. Please try again.');
      console.error(err);
    } finally {
      setGoogleLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8 mt-16">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-md">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            {otpSent ? 'Enter verification code' : 'Welcome back'}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {otpSent
              ? 'Enter the code we sent to your email'
              : 'Sign in to your account to continue'
            }
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 border-l-4 border-green-500 p-4 rounded">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <p className="text-sm text-green-700">{successMessage}</p>
            </div>
          </div>
        )}

        {!otpSent && (
          <div className="mt-6">
            <Button
              type="button"
              variant="outline"
              className="w-full flex items-center justify-center"
              onClick={handleGoogleSignIn}
              isLoading={googleLoading}
            >
              {!googleLoading && <GoogleIcon className="mr-2" size={20} />}
              Sign in with Google
            </Button>
          </div>
        )}

        {!otpSent && (
          <div className="mt-6 relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or continue with email</span>
            </div>
          </div>
        )}

        <form className="mt-6 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {!otpSent && (
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail size={18} className="text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
              </div>
            )}

            {otpSent && (
              <div>
                <label htmlFor="otpCode" className="block text-sm font-medium text-gray-700">
                  Verification Code
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <KeyRound size={18} className="text-gray-400" />
                  </div>
                  <input
                    id="otpCode"
                    name="otpCode"
                    type="text"
                    autoComplete="one-time-code"
                    required
                    className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123456"
                    value={otpCode}
                    onChange={(e) => setOtpCode(e.target.value)}
                  />
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Check your email for the verification code
                </p>
              </div>
            )}
          </div>

          {otpSent && (
            <div className="flex items-center justify-between">
              <div className="text-sm">
                <button
                  type="button"
                  onClick={resetForm}
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Use a different email
                </button>
              </div>
              <div className="text-sm">
                <button
                  type="button"
                  onClick={() => requestOtp(email)}
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Resend code
                </button>
              </div>
            </div>
          )}

          <div>
            <Button
              type="submit"
              variant="primary"
              className="w-full"
              isLoading={isLoading}
            >
              {otpSent ? 'Verify Code' : 'Send Verification Code'}
            </Button>
          </div>
        </form>

        {!otpSent && (
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link to="/register" className="font-medium text-blue-600 hover:text-blue-500">
                Sign up
              </Link>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Login;