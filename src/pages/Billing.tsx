import React, { useState } from 'react';
import { CreditCard, Plus, Wallet, AlertCircle, CheckCircle, ArrowUp, ArrowDown, RefreshCw, Gift } from 'lucide-react';
import Button from '../components/common/Button';
import { useBilling } from '../hooks/useBilling';
import { formatIDR, formatNumber } from '../utils/formatters';
import TopUpModal from '../components/modals/TopUpModal';
import Pagination from '../components/billing/Pagination';
import { usePageTitle } from '../hooks/usePageTitle';

const Billing = () => {
  usePageTitle('Billing');

  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [isTopUpModalOpen, setIsTopUpModalOpen] = useState(false);

  const {
    billingPlans,
    userBalance,
    transactions,
    loading,
    error,
    purchaseCredits,
    purchaseLoading,
    purchaseError,
    purchaseSuccess,
    resetPurchaseState,
    pagination
  } = useBilling();

  const handlePurchase = async (planId: string) => {
    setSelectedPlanId(planId);
    await purchaseCredits(planId);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Billing</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your balance and view transaction history
          </p>
        </div>
        <Button
          variant="primary"
          onClick={() => setIsTopUpModalOpen(true)}
        >
          <Plus size={18} className="mr-2" />
          Add Credit
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Purchase error and success messages - Hidden for now
      {purchaseError && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-700">{purchaseError}</p>
          </div>
        </div>
      )}

      {purchaseSuccess && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 rounded">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <p className="text-sm text-green-700">Credits purchased successfully!</p>
          </div>
        </div>
      )}
      */}

      {/* Current Balance */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Wallet className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">Current Credits</p>
                {loading.balance ? (
                  <p className="text-2xl font-bold text-gray-900">Loading...</p>
                ) : (
                  <p className="text-2xl font-bold text-gray-900">{userBalance?.credits ? formatIDR(userBalance.credits, 0) : 'Rp 0'}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Credit Packages - Hidden for now
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Purchase Credits</h2>
        </div>
        <div className="px-6 py-5">
          {loading.plans ? (
            <p>Loading available plans...</p>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {billingPlans.map((plan) => (
                <div
                  key={plan.id}
                  className={`border rounded-lg p-4 ${selectedPlanId === plan.id ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{plan.name}</h3>
                      <p className="text-sm text-gray-500">{plan.description}</p>
                    </div>
                    <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <CreditCard className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-2xl font-bold text-gray-900">{formatIDR(plan.price)}</p>
                    <p className="text-sm text-gray-500">{formatNumber(plan.credits, 0)} credits</p>
                  </div>
                  <div className="mt-4">
                    <Button
                      variant="primary"
                      className="w-full"
                      onClick={() => handlePurchase(plan.id)}
                      isLoading={purchaseLoading && selectedPlanId === plan.id}
                      disabled={purchaseLoading}
                    >
                      Purchase
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      */}

      {/* Transaction History */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Transaction History</h2>
        </div>
        {loading.transactions ? (
          <div className="px-6 py-5 text-center">
            <p>Loading transactions...</p>
          </div>
        ) : transactions.length === 0 ? (
          <div className="px-6 py-5 text-center">
            <p className="text-gray-500">No transactions found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr key={transaction.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(transaction.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {transaction.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className="inline-flex items-center">
                        {transaction.transactionType === 'purchase' && (
                          <ArrowUp className="h-4 w-4 mr-1 text-green-500" />
                        )}
                        {transaction.transactionType === 'usage' && (
                          <ArrowDown className="h-4 w-4 mr-1 text-red-500" />
                        )}
                        {transaction.transactionType === 'refund' && (
                          <RefreshCw className="h-4 w-4 mr-1 text-blue-500" />
                        )}
                        {transaction.transactionType === 'bonus' && (
                          <Gift className="h-4 w-4 mr-1 text-purple-500" />
                        )}
                        <span className={`capitalize ${
                          transaction.transactionType === 'purchase' || transaction.transactionType === 'bonus'
                            ? 'text-green-600'
                            : transaction.transactionType === 'refund'
                              ? 'text-blue-600'
                              : 'text-red-600'
                        }`}>
                          {transaction.transactionType}
                        </span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                      <span className={`font-medium ${
                        transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.amount > 0 ? '+' : ''}Rp {formatNumber(transaction.amount, 0)} credits
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                onPageChange={pagination.setPage}
              />
            )}
          </div>
        )}
      </div>

      {/* TopUp Modal */}
      <TopUpModal
        isOpen={isTopUpModalOpen}
        onClose={() => setIsTopUpModalOpen(false)}
      />
    </div>
  );
};

export default Billing;