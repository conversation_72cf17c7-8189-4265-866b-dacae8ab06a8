import React, { useState, useMemo } from 'react';
import { ArrowLeft, Cloud, Search } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import Button from '../components/common/Button';
import { useServiceTemplateGroupsWithPricing } from '../hooks/useServiceTemplates';
import { usePageTitle } from '../hooks/usePageTitle';

// Add CSS for description container with minimum 2-line height and ellipsis
const descriptionStyles = `
  .description-container {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    min-height: 2.8em; /* Ensures minimum 2 lines height */
  }
`;



const CreateService = () => {
  usePageTitle('Add Service');

  const navigate = useNavigate();
  const { groups, loading: groupsLoading, error: groupsError } = useServiceTemplateGroupsWithPricing();
  const [searchQuery, setSearchQuery] = useState('');

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleGroupSelect = (type: string) => {
    navigate(`/dashboard/services/create/${type}`);
  };

  // Filter groups based on search query
  const filteredGroups = useMemo(() => {
    return groups.filter(group =>
      searchQuery === '' ||
      group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.type.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [groups, searchQuery]);

  return (
    <div className="space-y-6">
      {/* Apply the CSS styles */}
      <style dangerouslySetInnerHTML={{ __html: descriptionStyles }} />

      <div className="flex items-center">
        <Link to="/dashboard/services" className="mr-4">
          <Button variant="secondary" size="sm">
            <ArrowLeft size={16} className="mr-2" />
            Back
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Add Service</h1>
          <p className="mt-1 text-sm text-gray-500">
            Choose a service template to add to your account
          </p>
        </div>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Search service categories..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Service Template Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {groupsLoading ? (
          <div className="col-span-3 py-10 text-center text-gray-500">
            Loading service categories...
          </div>
        ) : groupsError ? (
          <div className="col-span-3 py-10 text-center text-red-500">
            Error loading categories: {groupsError}
          </div>
        ) : filteredGroups.length === 0 ? (
          <div className="col-span-3 py-10 text-center text-gray-500">
            {searchQuery ? 'No service categories match your search' : 'No service categories found'}
          </div>
        ) : filteredGroups.map((group) => (
          <div
            key={group.id}
            className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleGroupSelect(group.type)}
          >
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                <Cloud className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">{group.name}</h3>
              <p className="mt-2 text-sm text-gray-500 description-container">{group.description}</p>

              <div className="mt-4 text-sm text-gray-500">
                {group.minPrice === 0 ? 'Starts from FREE' : `Starts from ${formatPrice(group.minPrice)}/month`}
              </div>

              <div className="mt-4">
                <Button
                  variant="primary"
                  className="w-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGroupSelect(group.type);
                  }}
                >
                  <Cloud size={18} className="mr-2" />
                  Deploy
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CreateService;
