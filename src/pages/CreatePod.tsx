import React, { useState } from 'react';
import { ArrowLeft, Box, Github, ExternalLink, Search } from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import PodConfigModal from '../components/modals/PodConfigModal';
import { useTemplates } from '../hooks/useTemplates';
import { formatIDR } from '../utils/formatters';
import { usePageTitle } from '../hooks/usePageTitle';

const CreatePod = () => {
  usePageTitle('Create Pod');

  const {
    templates,
    searchQuery,
    setSearchQuery
  } = useTemplates();

  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  const handleDeploy = (template: any) => {
    setSelectedTemplate(template);
    setConfigModalOpen(true);
  };

  const handleConfigSubmit = (config: any) => {
    console.log('Pod configuration:', config);
    setConfigModalOpen(false);
    // Handle pod creation here
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/dashboard"
            className="inline-flex items-center justify-center p-2 rounded-lg text-gray-500 hover:text-gray-900 hover:bg-gray-100"
          >
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Pod</h1>
            <p className="mt-1 text-sm text-gray-500">
              Select a template to get started
            </p>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500">No templates found matching your criteria</p>
          </div>
        ) : (
          templates.map((template) => (
            <div
              key={template.id}
              className="bg-white rounded-lg border border-gray-200 hover:border-blue-500 transition-colors p-6 flex flex-col"
            >
              <div className="flex items-start justify-between">
                <div className="p-2 bg-blue-50 rounded-lg">
                  {template.icon}
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold text-gray-900">{formatIDR(template.price)}</p>
                  <p className="text-sm text-gray-500">/month</p>
                </div>
              </div>
              <div className="mt-4 flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  {template.name}
                </h3>
                <div className="flex items-center space-x-3">
                  <a
                    href={template.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                    title="Visit website"
                  >
                    <ExternalLink size={18} />
                  </a>
                  <a
                    href={template.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                    title="View source on GitHub"
                  >
                    <Github size={18} />
                  </a>
                </div>
              </div>
              <p className="mt-2 text-sm text-gray-500 flex-grow">
                {template.description}
              </p>
              <div className="mt-4 flex flex-wrap gap-2">
                {template.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              <div className="mt-6">
                <Button
                  variant="primary"
                  className="w-full"
                  onClick={() => handleDeploy(template)}
                >
                  <Box size={18} className="mr-2" />
                  Deploy
                </Button>
              </div>
            </div>
          ))
        )}
      </div>

      {selectedTemplate && (
        <PodConfigModal
          isOpen={configModalOpen}
          onClose={() => setConfigModalOpen(false)}
          onSubmit={handleConfigSubmit}
          templateName={selectedTemplate.name}
          templateIcon={selectedTemplate.icon}
        />
      )}
    </div>
  );
};

export default CreatePod;