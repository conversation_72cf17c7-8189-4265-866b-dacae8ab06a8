import { useEffect } from 'react';

/**
 * Custom hook to dynamically set the page title
 * @param title - The title to set for the page
 * @param suffix - Optional suffix to append (defaults to "SumoPod")
 */
export const usePageTitle = (title: string, suffix: string = 'SumoPod') => {
  useEffect(() => {
    const fullTitle = title ? `${title} - ${suffix}` : suffix;
    document.title = fullTitle;
    
    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = suffix;
    };
  }, [title, suffix]);
};

export default usePageTitle;
