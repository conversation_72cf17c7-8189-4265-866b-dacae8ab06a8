import React from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import AppRoutes from './routes';
import { AuthProvider } from './context/AuthContext';
import { ProfileProvider } from './context/ProfileContext';
import WhatsAppWidget from './components/WhatsAppWidget';

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <ProfileProvider>
          <AppRoutes />
          <WhatsAppWidget phoneNumber="6288980925856" />
        </ProfileProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;