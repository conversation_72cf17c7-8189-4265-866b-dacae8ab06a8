import React from 'react';
import { Link } from 'react-router-dom';
import { Cloud, ArrowRight } from 'lucide-react';
import Button from '../common/Button';
import { ServiceTemplateGroup } from '../../hooks/useServiceTemplates';

// Add CSS for description container with 2-line limit and ellipsis
const descriptionStyles = `
  .description-container {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
    min-height: 2.6em;
  }
`;

interface TemplateGroupCardProps {
  group: ServiceTemplateGroup & { minPrice?: number };
}

const TemplateGroupCard: React.FC<TemplateGroupCardProps> = ({ group }) => {
  // Format price to IDR
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      {/* Apply the CSS styles */}
      <style dangerouslySetInnerHTML={{ __html: descriptionStyles }} />

      <div className="p-6">
        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
          <Cloud className="h-6 w-6 text-blue-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">{group.name}</h3>
        <p className="text-sm text-gray-500 mb-4 description-container">{group.description}</p>
        
        <div className="flex items-center justify-between mb-4">
          {group.minPrice !== undefined ? (
            <div className="text-lg font-bold text-gray-900">
              Starting from {formatPrice(group.minPrice)}
            </div>
          ) : (
            <div className="text-sm text-gray-500">View pricing</div>
          )}
          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{group.type}</div>
        </div>
        
        <div className="flex justify-between items-center">
          <Link to={`/dashboard/services/create/${group.type}`}>
            <Button variant="primary" size="sm" className="flex items-center">
              Deploy
              <ArrowRight size={14} className="ml-1" />
            </Button>
          </Link>
          <Link to={`/dashboard/services/create/${group.type}`}>
            <Button variant="secondary" size="sm">
              View Templates
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TemplateGroupCard;
